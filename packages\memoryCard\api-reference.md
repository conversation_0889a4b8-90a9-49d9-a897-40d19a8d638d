# 记忆卡片系统 API 参考

## IMemoryCardService 接口

### 用户记忆管理

#### getUserMemory(userId: string)
获取指定用户的记忆数据。

**参数：**
- `userId: string` - 用户唯一标识

**返回：**
- `Promise<MemoryCardOperationResult<UserMemory>>` - 操作结果

**示例：**
```typescript
const result = await memoryService.getUserMemory("user123");
if (result.success) {
    console.log("用户记忆:", result.data);
} else {
    console.error("获取失败:", result.error);
}
```

#### createUserMemory(userId: string, userName?: string)
为新用户创建记忆数据结构。

**参数：**
- `userId: string` - 用户唯一标识
- `userName?: string` - 用户名称（可选）

**返回：**
- `Promise<MemoryCardOperationResult<UserMemory>>` - 操作结果

#### updateUserProfile(userId: string, profile: Partial<UserMemory["profile"]>)
更新用户画像信息。

**参数：**
- `userId: string` - 用户唯一标识
- `profile: Partial<UserMemory["profile"]>` - 要更新的画像信息

**返回：**
- `Promise<MemoryCardOperationResult<UserMemory>>` - 操作结果

### 记忆卡片管理

#### createMemoryCard(userId: string, card: Omit<MemoryCard, "card_id" | "created_at" | "last_accessed">)
为用户创建新的记忆卡片。

**参数：**
- `userId: string` - 用户唯一标识
- `card: Omit<MemoryCard, "card_id" | "created_at" | "last_accessed">` - 卡片数据

**返回：**
- `Promise<MemoryCardOperationResult<MemoryCard>>` - 操作结果

**示例：**
```typescript
const result = await memoryService.createMemoryCard("user123", {
    type: "Event",
    content: "用户提到要参加会议",
    keywords: ["会议", "工作"],
    source_message_ids: ["msg_001"],
    importance: 0.7,
    state: "Active"
});
```

#### getMemoryCard(userId: string, cardId: string)
获取指定的记忆卡片。

**参数：**
- `userId: string` - 用户唯一标识
- `cardId: string` - 卡片唯一标识

**返回：**
- `Promise<MemoryCardOperationResult<MemoryCard>>` - 操作结果

#### updateMemoryCard(userId: string, cardId: string, updates: Partial<MemoryCard>)
更新记忆卡片信息。

**参数：**
- `userId: string` - 用户唯一标识
- `cardId: string` - 卡片唯一标识
- `updates: Partial<MemoryCard>` - 要更新的字段

**返回：**
- `Promise<MemoryCardOperationResult<MemoryCard>>` - 操作结果

#### deleteMemoryCard(userId: string, cardId: string)
删除指定的记忆卡片。

**参数：**
- `userId: string` - 用户唯一标识
- `cardId: string` - 卡片唯一标识

**返回：**
- `Promise<MemoryCardOperationResult<void>>` - 操作结果

#### searchMemoryCards(options: MemoryCardSearchOptions)
搜索记忆卡片。

**参数：**
- `options: MemoryCardSearchOptions` - 搜索选项

**返回：**
- `Promise<MemoryCardOperationResult<MemoryCard[]>>` - 操作结果

**搜索选项：**
```typescript
interface MemoryCardSearchOptions {
    userId: string;
    keywords?: string[];
    types?: MemoryCard["type"][];
    states?: MemoryCard["state"][];
    minImportance?: number;
    timeRange?: {
        start?: string;
        end?: string;
    };
    limit?: number;
    sortBy?: "importance" | "created_at" | "last_accessed";
    sortOrder?: "asc" | "desc";
}
```

### 触发器系统

#### checkTriggers(userId: string, messageContent: string, currentTime?: Date)
检查用户记忆中的触发器。

**参数：**
- `userId: string` - 用户唯一标识
- `messageContent: string` - 消息内容
- `currentTime?: Date` - 当前时间（可选，默认为当前时间）

**返回：**
- `Promise<TriggerCheckResult[]>` - 触发结果列表

**示例：**
```typescript
const triggers = await memoryService.checkTriggers("user123", "我想喝咖啡");
for (const trigger of triggers) {
    if (trigger.triggered) {
        console.log("触发原因:", trigger.reason);
        console.log("建议提示:", trigger.prompt);
    }
}
```

### 后续追踪系统

#### createFollowUpTask(task: Omit<FollowUpTask, "id" | "createdAt">)
创建后续追踪任务。

**参数：**
- `task: Omit<FollowUpTask, "id" | "createdAt">` - 任务数据

**返回：**
- `Promise<MemoryCardOperationResult<FollowUpTask>>` - 操作结果

#### getFollowUpTasks(userId?: string, status?: FollowUpTask["status"])
获取后续追踪任务。

**参数：**
- `userId?: string` - 用户唯一标识（可选）
- `status?: FollowUpTask["status"]` - 任务状态（可选）

**返回：**
- `Promise<MemoryCardOperationResult<FollowUpTask[]>>` - 操作结果

#### completeFollowUpTask(taskId: string)
标记后续追踪任务为已完成。

**参数：**
- `taskId: string` - 任务唯一标识

**返回：**
- `Promise<MemoryCardOperationResult<void>>` - 操作结果

### 记忆简报生成

#### generateMemoryBrief(userId: string, messageContent?: string)
生成用户的记忆简报。

**参数：**
- `userId: string` - 用户唯一标识
- `messageContent?: string` - 消息内容（可选）

**返回：**
- `Promise<MemoryCardOperationResult<MemoryBrief>>` - 操作结果

**记忆简报结构：**
```typescript
interface MemoryBrief {
    userId: string;
    triggeredCards: MemoryCard[];
    relevantCards: MemoryCard[];
    pendingFollowUps: FollowUpTask[];
    prompts: string[];
    generatedAt: string;
}
```

### 信息处理

#### processMessage(userId: string, messageId: string, content: string, isFromUser?: boolean)
处理消息并自动生成记忆卡片。

**参数：**
- `userId: string` - 用户唯一标识
- `messageId: string` - 消息唯一标识
- `content: string` - 消息内容
- `isFromUser?: boolean` - 是否来自用户（默认为true）

**返回：**
- `Promise<MemoryCard[]>` - 生成的记忆卡片列表

## 工具函数

### generateCardId()
生成记忆卡片的唯一标识。

**返回：**
- `string` - 卡片ID

### generateFollowUpTaskId()
生成后续追踪任务的唯一标识。

**返回：**
- `string` - 任务ID

### validateMemoryCard(card: Partial<MemoryCard>)
验证记忆卡片数据的完整性。

**参数：**
- `card: Partial<MemoryCard>` - 要验证的卡片数据

**返回：**
- `MemoryCardOperationResult<MemoryCard>` - 验证结果

### createDefaultUserMemory(userId: string, userName?: string)
创建默认的用户记忆结构。

**参数：**
- `userId: string` - 用户唯一标识
- `userName?: string` - 用户名称（可选）

**返回：**
- `UserMemory` - 默认用户记忆

### 触发器检查函数

#### isTimeTriggerActive(trigger: TimeTrigger, currentTime?: Date)
检查时间触发器是否激活。

#### isContextTriggerActive(trigger: ContextTrigger, messageContent: string)
检查上下文触发器是否激活。

#### isTimeAndContextTriggerActive(trigger: TimeAndContextTrigger, messageContent: string, currentTime?: Date)
检查时间和上下文复合触发器是否激活。

#### calculateFollowUpScheduleTime(followUp: TimeDelayedFollowUp, cardArchivedAt: Date)
计算后续追踪任务的执行时间。

## 错误处理

所有API方法都返回 `MemoryCardOperationResult` 类型的结果：

```typescript
interface MemoryCardOperationResult<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    code?: string;
}
```

**常见错误代码：**
- `USER_MEMORY_NOT_FOUND` - 用户记忆不存在
- `CARD_NOT_FOUND` - 记忆卡片不存在
- `CARD_LIMIT_EXCEEDED` - 卡片数量超过限制
- `VALIDATION_ERROR` - 数据验证失败
- `DATABASE_ERROR` - 数据库操作失败

## 事件和钩子

系统提供以下事件钩子（如果需要的话）：

- `memory-card:created` - 记忆卡片创建时触发
- `memory-card:updated` - 记忆卡片更新时触发
- `memory-card:deleted` - 记忆卡片删除时触发
- `trigger:activated` - 触发器激活时触发
- `follow-up:scheduled` - 后续追踪任务计划时触发
