// =================================================================
// UTILITY FUNCTIONS
// =================================================================

import {
    ContextTrigger,
    MemoryCard,
    MemoryCardOperationResult,
    TimeAndContextTrigger,
    TimeDelayedFollowUp,
    TimeTrigger,
    UserMemory,
} from "./interfaces";

/**
 * 生成记忆卡片ID
 */
export function generateCardId(): string {
    return `card_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 生成后续追踪任务ID
 */
export function generateFollowUpTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 检查时间触发器是否激活
 */
export function isTimeTriggerActive(trigger: TimeTrigger, currentTime: Date = new Date()): boolean {
    const startDate = new Date(trigger.startDate);
    const endDate = trigger.endDate ? new Date(trigger.endDate) : null;

    if (currentTime < startDate) {
        return false;
    }

    if (endDate && currentTime > endDate) {
        return false;
    }

    return true;
}

/**
 * 检查上下文触发器是否激活
 */
export function isContextTriggerActive(trigger: ContextTrigger, messageContent: string): boolean {
    const content = messageContent.toLowerCase();
    return trigger.contextKeywords.some((keyword) => content.includes(keyword.toLowerCase()));
}

/**
 * 检查时间和上下文复合触发器是否激活
 */
export function isTimeAndContextTriggerActive(
    trigger: TimeAndContextTrigger,
    messageContent: string,
    currentTime: Date = new Date()
): boolean {
    // 首先检查时间条件
    const startDate = new Date(trigger.startDate);
    const endDate = new Date(trigger.endDate);

    if (currentTime < startDate || currentTime > endDate) {
        return false;
    }

    // 然后检查上下文条件
    const content = messageContent.toLowerCase();
    return trigger.contextKeywords.some((keyword) => content.includes(keyword.toLowerCase()));
}

/**
 * 计算后续追踪任务的执行时间
 */
export function calculateFollowUpScheduleTime(followUp: TimeDelayedFollowUp, cardArchivedAt: Date): Date {
    const scheduleTime = new Date(cardArchivedAt);
    scheduleTime.setDate(scheduleTime.getDate() + followUp.delayDays);
    return scheduleTime;
}

/**
 * 验证记忆卡片数据的完整性
 */
export function validateMemoryCard(card: Partial<MemoryCard>): MemoryCardOperationResult<MemoryCard> {
    const errors: string[] = [];

    if (!card.card_id) {
        errors.push("缺少卡片ID");
    }

    if (!card.type) {
        errors.push("缺少卡片类型");
    }

    if (!card.content || card.content.trim().length === 0) {
        errors.push("缺少卡片内容");
    }

    if (!card.keywords || card.keywords.length === 0) {
        errors.push("缺少关键词");
    }

    if (typeof card.importance !== "number" || card.importance < 0 || card.importance > 1) {
        errors.push("重要性分数必须在0-1之间");
    }

    if (!card.state) {
        errors.push("缺少卡片状态");
    }

    if (errors.length > 0) {
        return {
            success: false,
            error: `记忆卡片验证失败: ${errors.join(", ")}`,
            code: "VALIDATION_ERROR",
        };
    }

    return {
        success: true,
        data: card as MemoryCard,
    };
}

/**
 * 创建默认的用户记忆结构
 */
export function createDefaultUserMemory(userId: string, userName?: string): UserMemory {
    return {
        user_id: userId,
        profile: {
            name: userName || "未知用户",
            personas: [],
            summary: "新用户，暂无详细信息。",
        },
        memory_cards: [],
    };
}
