### **一、 配置指南**

#### **首次启动须知**

插件启动有两项**必要条件**，配置不满足将无法启动：

1.  **配置可用模型**：至少需要配置一个能正常使用的模型。
![image1.png](./assets/image1.png)
![image2.png](./assets/image2.png)

2.  **指定响应频道**：必须添加至少一个允许 Bot 响应的频道。
![image3.png](./assets/image3.png)

#### **模型组配置**

模型组的配置选项依赖于已有的模型，因此请**先成功启动一次插件**。

只有启动插件，这里的选项才会显示出来：
![image4.png](./assets/image4.png)

启动后：
![image6.png](./assets/image6.png)

**配置步骤：**

1.  **创建模型组**：在配置界面找到「模型组」设置，点击“添加”创建新的一行，并展开进行编辑。

2.  **填写信息**：
    *   **模型组名称**：为其命名，例如 `default`。
    *   **选择模型**：从下拉列表中选择要加入该组的模型。此列表会自动显示您已配置好的可用模型。

3.  **保存并应用**：点击「重载配置」按钮保存更改。新的模型组会立即生效，并出现在可选项中。

> **💡 提示**：无论是选择模型还是模型组，都强烈建议**从下拉列表中选择预设项**，而不是手动输入名称，以避免因拼写错误导致配置失败。

---

### **二、 记忆库配置**

#### **文件位置**

*   记忆文件默认存放于 `data/yesimbot/memory/core/` 目录下。
*   插件会自动加载此目录中所有**格式正确**的记忆文件。

#### **文件格式**

文件必须使用 **YAML Front Matter** 来定义元信息，其下方为记忆正文。

**元信息（必填部分）：**
```yaml
---
title: string # (可选)
label: string # (必填)
limit: number # (必填)
description: string # (可选) 描述信息
---

这里是记忆的正文内容，格式与旧版一致。
```

**关键点：**
*   `label`, `limit` 为**必填字段**，缺少任何一个都会导致该文件加载失败。
*   `description` 为可选字段。