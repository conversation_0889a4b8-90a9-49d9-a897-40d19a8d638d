name: Publish Core Package  
  
on:  
  push:  
    branches:  
      - main-v3
    paths:  
      - 'packages/core/**'  
      - 'package.json'  
      - 'yakumo.yml'  
  
jobs:  
  publish-core:  
    runs-on: ubuntu-latest  
      
    steps:  
      - uses: actions/checkout@v4  
        
      - name: Setup Node  
        uses: actions/setup-node@v4  
        with:  
          node-version: '18'  
            
      - name: Enable Corepack  
        run: corepack enable  
          
      - name: Cache Yarn dependencies  
        uses: ./.github/actions/cache  
        with:  
          os: ${{ runner.os }}  
            
      - name: Install dependencies  
        run: yarn install  
          
      - name: Build core package  
        run: yarn build:core  
          
      - name: Set npm token  
        run: |  
          echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc  
            
      - name: Publish core package  
        run: yarn pub:core
