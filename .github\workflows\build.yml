name: Build

on:
  push:
  pull_request:
    branches: ["main"]
    types: [opened, synchronize]

jobs:
  build:
    name: Build and Test
    timeout-minutes: 15
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.2.0

      - name: Install dependencies
        run: bun install

      - name: Build
        run: bun run build

    #   - name: Test
    #     run: bun run test
