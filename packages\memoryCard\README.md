# 记忆卡片系统 (Memory Card System)

基于卡片的主动记忆系统，实现AI的隐性捕捉、情境触发和长期追踪能力。

## 🎯 核心理念

### 三大核心理念

1. **隐性捕捉 (Implicit Capture)**
   - 从日常对话中自动、无干扰地提取关键信息
   - 无需用户显式下达"记住这个"的指令
   - 支持事实、事件、偏好、目标、关系等多种信息类型

2. **情境触发 (Contextual Trigger)**
   - 在特定时间、特定对话情境下主动联想并激活相关记忆
   - 支持时间触发、上下文触发、复合触发等多种触发方式
   - 使AI的互动更具即时相关性与同理心

3. **长期追踪 (Long-term Follow-up)**
   - 对重要的、有时效性的事件进行持续追踪
   - 在事件结束后适时地进行主动关怀和询问
   - 形成完整的交互闭环

### 愿景目标

实现如同设计文档中所述的愿景：
> "当我不经意间说要考试，她会默默记下，在我熬夜时提醒我；几天后，还会主动问我考得怎么样。"

## 🏗️ 系统架构

### 分层设计

```
应用层 (Application Layer)
├── 记忆检索器 (Retrieval Engine)
├── 记忆简报生成器 (Memory Brief Generator)
└── Prompt注入器 (Prompt Injector)

处理层 (Processing Layer)
├── 信息捕获器 (Capture Engine)
├── 记忆提炼器 (Refinery Engine)
├── 情境触发器 (Context Trigger Engine)
└── 长期追踪器 (Follow-up Engine)

存储层 (Storage Layer)
├── 用户记忆 (UserMemory)
├── 记忆卡片 (MemoryCard)
└── 后续追踪任务 (FollowUpTask)
```

## 📊 数据结构

### 核心接口

#### UserMemory - 用户记忆
```typescript
interface UserMemory {
    user_id: string;           // 用户唯一标识
    profile: UserProfile;      // 用户画像
    memory_cards: MemoryCard[]; // 记忆卡片集合
}
```

#### MemoryCard - 记忆卡片
```typescript
interface MemoryCard {
    card_id: string;           // 卡片唯一标识
    type: "Fact" | "Event" | "Preference" | "Goal" | "Relationship";
    content: string;           // 记忆内容
    keywords: string[];        // 关键词
    source_message_ids: string[]; // 来源消息ID
    created_at: string;        // 创建时间
    last_accessed: string;     // 最后访问时间
    importance: number;        // 重要性分数 (0.0-1.0)
    state: "Active" | "Fading" | "Archived"; // 状态
    trigger?: Trigger;         // 可选触发器
    followUp?: FollowUp;       // 可选后续追踪
}
```

### 触发器类型

#### 时间触发器
```typescript
interface TimeTrigger {
    type: "Time";
    startDate: string;    // 开始时间
    endDate?: string;     // 结束时间（可选）
    description?: string; // 描述
}
```

#### 上下文触发器
```typescript
interface ContextTrigger {
    type: "Context";
    contextKeywords: string[]; // 触发关键词
    description?: string;
}
```

#### 复合触发器
```typescript
interface TimeAndContextTrigger {
    type: "TimeAndContext";
    startDate: string;
    endDate: string;
    contextKeywords: string[];
    description?: string;
}
```

## 🚀 快速开始

### 1. 配置服务

```typescript
import { MemoryCardService, MemoryCardServiceConfig } from "./services/memoryCard";

const config: MemoryCardServiceConfig = {
    enabled: true,
    maxCardsPerUser: 1000,
    cleanupIntervalHours: 24,
    importanceDecayFactor: 0.95,
    followUpCheckIntervalMinutes: 60,
    captureEngine: {
        maxMessagesPerSegment: 20,
        segmentTimeoutMinutes: 30,
        minSegmentLength: 3
    },
    refineryEngine: {
        autoRefineEnabled: true,
        refineThreshold: 5,
        maxImportanceScore: 1.0
    },
    contextTriggerEngine: {
        enabled: true,
        checkIntervalMs: 1000,
        maxTriggerResults: 10
    },
    followUpEngine: {
        enabled: true,
        checkIntervalMinutes: 60,
        maxTasksPerCheck: 20,
        taskExpirationDays: 30
    }
};

// 注册服务
const memoryCardService = ctx.plugin(MemoryCardService, config);
```

### 2. 基础使用

```typescript
// 获取服务实例
const memoryService = ctx["yesimbot.memory-card"];

// 创建用户记忆
const userMemory = await memoryService.createUserMemory("user123", "张三");

// 创建记忆卡片
const card = await memoryService.createMemoryCard("user123", {
    type: "Event",
    content: "用户提到下周要参加PMP考试",
    keywords: ["PMP", "考试", "下周"],
    source_message_ids: ["msg_001"],
    importance: 0.8,
    state: "Active",
    trigger: {
        type: "Time",
        startDate: "2024-01-15T09:00:00Z",
        description: "考试当天提醒"
    },
    followUp: {
        type: "TimeDelayed",
        delayDays: 3,
        prompt: "询问PMP考试结果如何"
    }
});

// 检查触发器
const triggers = await memoryService.checkTriggers("user123", "今天感觉有点紧张");

// 生成记忆简报
const brief = await memoryService.generateMemoryBrief("user123", "最近怎么样？");
```

### 3. 处理消息

```typescript
// 自动处理消息并生成记忆卡片
const newCards = await memoryService.processMessage(
    "user123",
    "msg_002", 
    "我明天要去面试，有点紧张",
    true
);

console.log(`生成了 ${newCards.length} 张新的记忆卡片`);
```

## 🔧 高级功能

### 搜索记忆卡片

```typescript
const searchResult = await memoryService.searchMemoryCards({
    userId: "user123",
    keywords: ["考试", "面试"],
    types: ["Event", "Goal"],
    states: ["Active"],
    minImportance: 0.5,
    limit: 10,
    sortBy: "importance",
    sortOrder: "desc"
});
```

### 管理后续追踪任务

```typescript
// 获取待处理任务
const tasks = await memoryService.getFollowUpTasks("user123", "pending");

// 完成任务
await memoryService.completeFollowUpTask("task_id");
```

## 🧪 测试

运行测试文件：

```bash
# 运行基础测试
node packages/core/src/services/memoryCard/test.ts
```

测试包括：
- 工具函数测试
- 触发器函数测试
- 服务基础功能测试
- 使用示例演示

## 📝 最佳实践

### 1. 记忆卡片设计

- **内容简洁明确**：每张卡片应包含单一、明确的信息
- **关键词精准**：选择能准确描述内容的关键词
- **重要性评估**：合理设置重要性分数，重要事件设置较高分数
- **状态管理**：及时更新卡片状态，避免过期信息干扰

### 2. 触发器配置

- **时间触发器**：用于提醒、纪念日等时间敏感事件
- **上下文触发器**：用于话题联想、偏好匹配
- **复合触发器**：用于复杂场景，如"深夜关心"

### 3. 后续追踪

- **延迟追踪**：适用于需要后续关注的事件
- **状态变更追踪**：适用于需要实时响应的状态变化

## 🔍 故障排除

### 常见问题

1. **服务启动失败**
   - 检查数据库连接
   - 验证配置参数
   - 查看日志输出

2. **记忆卡片创建失败**
   - 检查用户记忆是否存在
   - 验证卡片数据完整性
   - 确认未超过数量限制

3. **触发器不工作**
   - 检查触发器配置
   - 验证时间范围设置
   - 确认关键词匹配

## 📚 相关文档

- [记忆系统设计文档](./记忆系统设计文档.md)
- [API 参考文档](./api-reference.md)
- [配置指南](./configuration-guide.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进记忆卡片系统！
