/**
 * MemoryService 用户中心化重构后的测试用例
 * 验证用户事实管理、用户画像生成、缓存机制等核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Context } from 'koishi';
import { MemoryService } from '../MemoryService';
import { MemoryConfig } from '../config';
import { FactType, LifespanType } from '../types';

describe('MemoryService 用户中心化重构验证', () => {
    let ctx: Context;
    let memoryService: MemoryService;
    let mockConfig: MemoryConfig;

    beforeEach(() => {
        // 模拟配置
        mockConfig = {
            coreMemoryPath: 'test/memory',
            batching: {
                minSize: 3,
                maxSize: 10,
                maxWaitTime: 30
            },
            forgetting: {
                checkIntervalHours: 24,
                stalenessDays: 90,
                salienceThreshold: 0.3,
                accessCountThreshold: 2
            },
            profileGeneration: {
                factRelevanceThreshold: 0.3,
                maxSummaryLength: 500,
                updateIntervalHours: 6,
                minFactsForUpdate: 3,
                confidenceThreshold: 0.6,
                enableIncrementalUpdate: true,
                keyFactWeight: 1.5
            },
            caching: {
                enabled: true,
                profileCacheTtlMinutes: 30,
                factsCacheTtlMinutes: 15,
                maxCacheEntries: 1000,
                cleanupIntervalMinutes: 10
            },
            errorHandling: {
                maxRetries: 3,
                retryDelayMs: 1000,
                lockTimeoutMs: 30000,
                circuitBreakerThreshold: 5,
                circuitBreakerResetMs: 60000
            }
        };

        // 模拟 Context
        ctx = {
            database: {
                get: vi.fn(),
                create: vi.fn(),
                set: vi.fn()
            },
            logger: {
                info: vi.fn(),
                warn: vi.fn(),
                error: vi.fn(),
                debug: vi.fn()
            }
        } as any;

        memoryService = new MemoryService(ctx, mockConfig);
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('用户事实管理', () => {
        it('应该正确添加用户事实', async () => {
            // 模拟嵌入模型
            const mockEmbedding = [0.1, 0.2, 0.3];
            ctx.model = {
                embed: vi.fn().mockResolvedValue({ embedding: mockEmbedding })
            };

            const factData = {
                userId: 'user-123',
                userName: '张三',
                content: '张三喜欢编程',
                type: FactType.Preference,
                lifespan: LifespanType.Long,
                sourceMessageIds: ['msg-001'],
                salience: 0.8
            };

            ctx.database.create = vi.fn().mockResolvedValue({
                id: 'fact-1',
                ...factData,
                embedding: mockEmbedding,
                createdAt: new Date(),
                lastAccessedAt: new Date(),
                accessCount: 0
            });

            const result = await memoryService.addUserFact(factData);

            expect(result.success).toBe(true);
            expect(result.data).toBeDefined();
            expect(ctx.database.create).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    userId: 'user-123',
                    content: '张三喜欢编程',
                    type: FactType.Preference
                })
            );
        });

        it('应该正确验证输入参数', async () => {
            // 测试空的用户ID
            await expect(
                memoryService.consolidateProfile('')
            ).rejects.toThrow('用户ID不能为空');

            // 测试无效的用户ID
            await expect(
                memoryService.consolidateProfile('   ')
            ).rejects.toThrow('用户ID不能为空');
        });

        it('应该正确获取用户事实', async () => {
            const mockFacts = [
                {
                    id: 'fact-1',
                    userId: 'user-123',
                    userName: '张三',
                    content: '张三喜欢编程',
                    type: FactType.Preference,
                    lifespan: LifespanType.Long,
                    sourceMessageIds: ['msg-001'],
                    salience: 0.8,
                    createdAt: new Date()
                },
                {
                    id: 'fact-2',
                    userId: 'user-123',
                    userName: '张三',
                    content: '张三计划学习 Rust',
                    type: FactType.Plan,
                    lifespan: LifespanType.Short,
                    sourceMessageIds: ['msg-002'],
                    salience: 0.6,
                    createdAt: new Date()
                }
            ];

            ctx.database.get = vi.fn().mockResolvedValue(mockFacts);

            const result = await memoryService.getUserFacts('user-123');

            expect(result.success).toBe(true);
            expect(result.data).toHaveLength(2);
            expect(result.data[0].userId).toBe('user-123');
            expect(ctx.database.get).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    userId: 'user-123'
                })
            );
        });
    });

    describe('缓存机制', () => {
        it('应该正确缓存用户事实', async () => {
            const mockFacts = [
                {
                    id: 'fact-1',
                    userId: 'user-123',
                    content: '测试事实',
                    type: FactType.Statement,
                    lifespan: LifespanType.Long,
                    sourceMessageIds: ['msg-001'],
                    salience: 0.8,
                    createdAt: new Date()
                }
            ];

            ctx.database.get = vi.fn().mockResolvedValue(mockFacts);

            // 第一次调用应该查询数据库
            const result1 = await memoryService.getUserFacts('user-123');
            expect(result1.success).toBe(true);
            expect(ctx.database.get).toHaveBeenCalledTimes(1);

            // 第二次调用应该使用缓存（如果缓存启用）
            const result2 = await memoryService.getUserFacts('user-123');
            expect(result2.success).toBe(true);
            // 注意：实际的缓存行为取决于实现细节
        });

        it('应该正确应用配置参数', () => {
            expect(memoryService['profileGenerationConfig']).toEqual(mockConfig.profileGeneration);
            expect(memoryService['cachingConfig']).toEqual(mockConfig.caching);
        });
    });

    describe('用户画像管理', () => {
        it('应该正确处理用户画像整合', async () => {
            const mockProfile = {
                id: 'profile-1',
                userId: 'user-123',
                userName: '张三',
                content: '张三是一个喜欢编程的用户',
                confidence: 0.8,
                supportingFactIds: ['fact-1', 'fact-2'],
                updatedAt: new Date(Date.now() - 7 * 60 * 60 * 1000), // 7小时前
                version: 1
            };

            ctx.database.get = vi.fn().mockResolvedValue([mockProfile]);

            const result = await memoryService.getUserProfile('user-123');

            expect(result.success).toBe(true);
            expect(result.data?.userId).toBe('user-123');
            expect(ctx.database.get).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    userId: 'user-123'
                })
            );
        });

        it('应该正确处理并发控制', async () => {
            // 模拟并发调用
            const promises = Array.from({ length: 5 }, () =>
                memoryService.consolidateProfile('user-123')
            );

            // 所有调用都应该完成（即使有些失败）
            const results = await Promise.allSettled(promises);
            expect(results).toHaveLength(5);
        });
    });
});
