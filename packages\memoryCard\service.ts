import { Context, Service } from "koishi";

import { Services, TableName } from "@/services/types";
import { MemoryCardServiceConfig } from "./config";
import { RefineryEngine } from "./engines";
import {
    FollowUpTask,
    MemoryBrief,
    MemoryCard,
    MemoryCardOperationResult,
    MemoryCardSearchOptions,
    TriggerCheckResult,
    UserMemory,
} from "./interfaces";
import {
    createDefaultUserMemory,
    generateCardId,
    generateFollowUpTaskId,
    isContextTriggerActive,
    isTimeAndContextTriggerActive,
    isTimeTriggerActive,
    validateMemoryCard,
} from "./utils";

declare module "koishi" {
    interface Context {
        [Services.MemoryCard]: MemoryCardService;
    }
    interface Tables {
        [TableName.UserMemories]: UserMemory;
        [TableName.FollowUpTasks]: FollowUpTask;
    }
}

/**
 * 记忆卡片服务接口
 */
export interface IMemoryCardService {
    // === 用户记忆管理 ===
    getUserMemory(userId: string): Promise<MemoryCardOperationResult<UserMemory>>;
    createUserMemory(userId: string, userName?: string): Promise<MemoryCardOperationResult<UserMemory>>;
    updateUserProfile(
        userId: string,
        profile: Partial<UserMemory["profile"]>
    ): Promise<MemoryCardOperationResult<UserMemory>>;

    // === 记忆卡片管理 ===
    createMemoryCard(
        userId: string,
        card: Omit<MemoryCard, "card_id" | "created_at" | "last_accessed">
    ): Promise<MemoryCardOperationResult<MemoryCard>>;
    getMemoryCard(userId: string, cardId: string): Promise<MemoryCardOperationResult<MemoryCard>>;
    updateMemoryCard(
        userId: string,
        cardId: string,
        updates: Partial<MemoryCard>
    ): Promise<MemoryCardOperationResult<MemoryCard>>;
    deleteMemoryCard(userId: string, cardId: string): Promise<MemoryCardOperationResult<void>>;
    searchMemoryCards(options: MemoryCardSearchOptions): Promise<MemoryCardOperationResult<MemoryCard[]>>;

    // === 触发器系统 ===
    checkTriggers(userId: string, messageContent: string, currentTime?: Date): Promise<TriggerCheckResult[]>;

    // === 后续追踪系统 ===
    createFollowUpTask(task: Omit<FollowUpTask, "id" | "createdAt">): Promise<MemoryCardOperationResult<FollowUpTask>>;
    getFollowUpTasks(
        userId?: string,
        status?: FollowUpTask["status"]
    ): Promise<MemoryCardOperationResult<FollowUpTask[]>>;
    completeFollowUpTask(taskId: string): Promise<MemoryCardOperationResult<void>>;

    // === 记忆简报生成 ===
    generateMemoryBrief(userId: string, messageContent?: string): Promise<MemoryCardOperationResult<MemoryBrief>>;

    // === 信息处理 ===
    // processMessage(userId: string, messageId: string, content: string, isFromUser?: boolean): Promise<MemoryCard[]>;
}

/**
 * 记忆卡片服务实现
 */
export class MemoryCardService extends Service<MemoryCardServiceConfig> implements IMemoryCardService {
    static readonly inject = [Services.Logger, "database"];

    // private logger: Logger;
    private cleanupTimer?: NodeJS.Timeout;
    private followUpTimer?: NodeJS.Timeout;
    private refineryEngine?: RefineryEngine;

    constructor(ctx: Context, config: MemoryCardServiceConfig) {
        super(ctx, Services.MemoryCard, true);
        this.config = config;
        this.logger = ctx[Services.Logger].getLogger("[记忆卡片服务]");
    }

    protected async start() {
        if (!this.config.enabled) {
            this.logger.info("服务已禁用");
            return;
        }

        this.registerDatabaseModels();
        this.initializeEngines();
        this.startBackgroundTasks();
        this.logger.info("服务已启动");
    }

    protected async stop() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        if (this.followUpTimer) {
            clearInterval(this.followUpTimer);
        }
        this.logger.info("服务已停止");
    }

    /**
     * 初始化引擎
     */
    private initializeEngines() {
        this.refineryEngine = new RefineryEngine(this.ctx, this.config.refineryEngine);
        this.logger.debug("引擎初始化完成");
    }

    /**
     * 注册数据库模型
     */
    private registerDatabaseModels() {
        // 用户记忆表
        this.ctx.model.extend(
            TableName.UserMemories,
            {
                user_id: "string(255)",
                profile: "json",
                memory_cards: "json",
            },
            { primary: "user_id" }
        );

        // 后续追踪任务表
        this.ctx.model.extend(
            TableName.FollowUpTasks,
            {
                id: "string(64)",
                cardId: "string(64)",
                userId: "string(255)",
                type: "string(32)",
                prompt: "text",
                scheduledAt: "string(32)", // ISO 8601 字符串
                createdAt: "string(32)", // ISO 8601 字符串
                status: "string(32)",
            },
            { primary: "id" }
        );
    }

    /**
     * 启动后台任务
     */
    private startBackgroundTasks() {
        // 定期清理任务
        this.cleanupTimer = setInterval(() => this.performCleanup(), this.config.cleanupIntervalHours * 60 * 60 * 1000);

        // 后续追踪任务检查
        this.followUpTimer = setInterval(
            () => this.checkFollowUpTasks(),
            this.config.followUpCheckIntervalMinutes * 60 * 1000
        );
    }

    /**
     * 获取用户记忆
     */
    async getUserMemory(userId: string): Promise<MemoryCardOperationResult<UserMemory>> {
        try {
            const userMemory = await this.ctx.database.get(TableName.UserMemories, { user_id: userId });

            if (userMemory.length === 0) {
                return {
                    success: false,
                    error: "用户记忆不存在",
                    code: "USER_MEMORY_NOT_FOUND",
                };
            }

            return {
                success: true,
                data: userMemory[0],
            };
        } catch (error) {
            this.logger.error(`获取用户记忆失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 创建用户记忆
     */
    async createUserMemory(userId: string, userName?: string): Promise<MemoryCardOperationResult<UserMemory>> {
        try {
            const defaultMemory = createDefaultUserMemory(userId, userName);

            await this.ctx.database.create(TableName.UserMemories, defaultMemory);

            return {
                success: true,
                data: defaultMemory,
            };
        } catch (error) {
            this.logger.error(`创建用户记忆失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 更新用户画像
     */
    async updateUserProfile(
        userId: string,
        profile: Partial<UserMemory["profile"]>
    ): Promise<MemoryCardOperationResult<UserMemory>> {
        try {
            const userMemoryResult = await this.getUserMemory(userId);
            if (!userMemoryResult.success) {
                return userMemoryResult;
            }

            const updatedProfile = { ...userMemoryResult.data!.profile, ...profile };

            await this.ctx.database.set(
                TableName.UserMemories,
                { user_id: userId },
                {
                    profile: updatedProfile,
                }
            );

            const updatedMemory = { ...userMemoryResult.data!, profile: updatedProfile };

            return {
                success: true,
                data: updatedMemory,
            };
        } catch (error) {
            this.logger.error(`更新用户画像失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 创建记忆卡片
     */
    async createMemoryCard(
        userId: string,
        card: Omit<MemoryCard, "card_id" | "created_at" | "last_accessed">
    ): Promise<MemoryCardOperationResult<MemoryCard>> {
        try {
            const now = new Date().toISOString();
            const newCard: MemoryCard = {
                ...card,
                card_id: generateCardId(),
                created_at: now,
                last_accessed: now,
            };

            // 验证卡片数据
            const validation = validateMemoryCard(newCard);
            if (!validation.success) {
                return validation;
            }

            // 获取用户记忆
            let userMemoryResult = await this.getUserMemory(userId);
            if (!userMemoryResult.success) {
                // 如果用户记忆不存在，创建一个
                const createResult = await this.createUserMemory(userId);
                if (!createResult.success) {
                    return {
                        success: false,
                        error: createResult.error,
                        code: createResult.code,
                    };
                }
                userMemoryResult = createResult;
            }

            const userMemory = userMemoryResult.data!;

            // 检查卡片数量限制
            if (userMemory.memory_cards.length >= this.config.maxCardsPerUser) {
                return {
                    success: false,
                    error: `用户记忆卡片数量已达到上限 (${this.config.maxCardsPerUser})`,
                    code: "CARD_LIMIT_EXCEEDED",
                };
            }

            // 添加新卡片
            userMemory.memory_cards.push(newCard);

            await this.ctx.database.set(
                TableName.UserMemories,
                { user_id: userId },
                {
                    memory_cards: userMemory.memory_cards,
                }
            );

            return {
                success: true,
                data: newCard,
            };
        } catch (error) {
            this.logger.error(`创建记忆卡片失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 执行清理任务
     */
    private async performCleanup() {
        this.logger.debug("开始执行记忆清理任务");
        // TODO: 实现记忆衰减和清理逻辑
    }

    /**
     * 检查后续追踪任务
     */
    private async checkFollowUpTasks() {
        this.logger.debug("开始检查后续追踪任务");
        // TODO: 实现后续追踪任务检查逻辑
    }

    /**
     * 获取记忆卡片
     */
    async getMemoryCard(userId: string, cardId: string): Promise<MemoryCardOperationResult<MemoryCard>> {
        try {
            const userMemoryResult = await this.getUserMemory(userId);
            if (!userMemoryResult.success) {
                return {
                    success: false,
                    error: userMemoryResult.error,
                    code: userMemoryResult.code,
                };
            }

            const card = userMemoryResult.data!.memory_cards.find((c) => c.card_id === cardId);
            if (!card) {
                return {
                    success: false,
                    error: "记忆卡片不存在",
                    code: "CARD_NOT_FOUND",
                };
            }

            // 更新最后访问时间
            card.last_accessed = new Date().toISOString();
            await this.ctx.database.set(
                TableName.UserMemories,
                { user_id: userId },
                {
                    memory_cards: userMemoryResult.data!.memory_cards,
                }
            );

            return {
                success: true,
                data: card,
            };
        } catch (error) {
            this.logger.error(`获取记忆卡片失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 更新记忆卡片
     */
    async updateMemoryCard(
        userId: string,
        cardId: string,
        updates: Partial<MemoryCard>
    ): Promise<MemoryCardOperationResult<MemoryCard>> {
        try {
            const userMemoryResult = await this.getUserMemory(userId);
            if (!userMemoryResult.success) {
                return {
                    success: false,
                    error: userMemoryResult.error,
                    code: userMemoryResult.code,
                };
            }

            const cardIndex = userMemoryResult.data!.memory_cards.findIndex((c) => c.card_id === cardId);
            if (cardIndex === -1) {
                return {
                    success: false,
                    error: "记忆卡片不存在",
                    code: "CARD_NOT_FOUND",
                };
            }

            // 更新卡片
            const updatedCard = {
                ...userMemoryResult.data!.memory_cards[cardIndex],
                ...updates,
                last_accessed: new Date().toISOString(),
            };

            // 验证更新后的卡片
            const validation = validateMemoryCard(updatedCard);
            if (!validation.success) {
                return validation;
            }

            userMemoryResult.data!.memory_cards[cardIndex] = updatedCard;

            await this.ctx.database.set(
                TableName.UserMemories,
                { user_id: userId },
                {
                    memory_cards: userMemoryResult.data!.memory_cards,
                }
            );

            return {
                success: true,
                data: updatedCard,
            };
        } catch (error) {
            this.logger.error(`更新记忆卡片失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 删除记忆卡片
     */
    async deleteMemoryCard(userId: string, cardId: string): Promise<MemoryCardOperationResult<void>> {
        try {
            const userMemoryResult = await this.getUserMemory(userId);
            if (!userMemoryResult.success) {
                return {
                    success: false,
                    error: userMemoryResult.error,
                    code: userMemoryResult.code,
                };
            }

            const cardIndex = userMemoryResult.data!.memory_cards.findIndex((c) => c.card_id === cardId);
            if (cardIndex === -1) {
                return {
                    success: false,
                    error: "记忆卡片不存在",
                    code: "CARD_NOT_FOUND",
                };
            }

            // 删除卡片
            userMemoryResult.data!.memory_cards.splice(cardIndex, 1);

            await this.ctx.database.set(
                TableName.UserMemories,
                { user_id: userId },
                {
                    memory_cards: userMemoryResult.data!.memory_cards,
                }
            );

            return {
                success: true,
            };
        } catch (error) {
            this.logger.error(`删除记忆卡片失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 搜索记忆卡片
     */
    async searchMemoryCards(options: MemoryCardSearchOptions): Promise<MemoryCardOperationResult<MemoryCard[]>> {
        try {
            const userMemoryResult = await this.getUserMemory(options.userId);
            if (!userMemoryResult.success) {
                return {
                    success: false,
                    error: userMemoryResult.error,
                    code: userMemoryResult.code,
                };
            }

            let cards = userMemoryResult.data!.memory_cards;

            // 应用过滤条件
            if (options.types && options.types.length > 0) {
                cards = cards.filter((card) => options.types!.includes(card.type));
            }

            if (options.states && options.states.length > 0) {
                cards = cards.filter((card) => options.states!.includes(card.state));
            }

            if (options.minImportance !== undefined) {
                cards = cards.filter((card) => card.importance >= options.minImportance!);
            }

            if (options.keywords && options.keywords.length > 0) {
                cards = cards.filter((card) =>
                    options.keywords!.some(
                        (keyword) =>
                            card.keywords.some((cardKeyword) =>
                                cardKeyword.toLowerCase().includes(keyword.toLowerCase())
                            ) || card.content.toLowerCase().includes(keyword.toLowerCase())
                    )
                );
            }

            if (options.timeRange) {
                if (options.timeRange.start) {
                    const startDate = new Date(options.timeRange.start);
                    cards = cards.filter((card) => new Date(card.created_at) >= startDate);
                }
                if (options.timeRange.end) {
                    const endDate = new Date(options.timeRange.end);
                    cards = cards.filter((card) => new Date(card.created_at) <= endDate);
                }
            }

            // 排序
            const sortBy = options.sortBy || "importance";
            const sortOrder = options.sortOrder || "desc";

            cards.sort((a, b) => {
                let aValue: any, bValue: any;

                switch (sortBy) {
                    case "importance":
                        aValue = a.importance;
                        bValue = b.importance;
                        break;
                    case "created_at":
                        aValue = new Date(a.created_at);
                        bValue = new Date(b.created_at);
                        break;
                    case "last_accessed":
                        aValue = new Date(a.last_accessed);
                        bValue = new Date(b.last_accessed);
                        break;
                    default:
                        aValue = a.importance;
                        bValue = b.importance;
                }

                if (sortOrder === "asc") {
                    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                } else {
                    return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
                }
            });

            // 限制结果数量
            if (options.limit && options.limit > 0) {
                cards = cards.slice(0, options.limit);
            }

            return {
                success: true,
                data: cards,
            };
        } catch (error) {
            this.logger.error(`搜索记忆卡片失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 检查触发器
     */
    async checkTriggers(userId: string, messageContent: string, currentTime?: Date): Promise<TriggerCheckResult[]> {
        try {
            const userMemoryResult = await this.getUserMemory(userId);
            if (!userMemoryResult.success) {
                return [];
            }

            const results: TriggerCheckResult[] = [];
            const now = currentTime || new Date();

            for (const card of userMemoryResult.data!.memory_cards) {
                if (!card.trigger || card.state === "Archived") {
                    continue;
                }

                let triggered = false;
                let reason = "";

                switch (card.trigger.type) {
                    case "Time":
                        triggered = isTimeTriggerActive(card.trigger, now);
                        if (triggered) {
                            reason = `时间触发器激活: ${card.trigger.description || "时间条件满足"}`;
                        }
                        break;

                    case "Context":
                        triggered = isContextTriggerActive(card.trigger, messageContent);
                        if (triggered) {
                            reason = `上下文触发器激活: 检测到关键词 [${card.trigger.contextKeywords.join(", ")}]`;
                        }
                        break;

                    case "TimeAndContext":
                        triggered = isTimeAndContextTriggerActive(card.trigger, messageContent, now);
                        if (triggered) {
                            reason = `时间和上下文复合触发器激活: 时间和关键词条件都满足`;
                        }
                        break;

                    case "EventCompletion":
                        // 检查目标卡片状态
                        const eventTrigger = card.trigger as any; // 类型断言
                        const targetCard = userMemoryResult.data!.memory_cards.find(
                            (c) => c.card_id === eventTrigger.targetCardId
                        );
                        if (targetCard && targetCard.state === eventTrigger.targetState) {
                            triggered = true;
                            reason = `事件完成触发器激活: 目标卡片 ${eventTrigger.targetCardId} 状态变为 ${eventTrigger.targetState}`;
                        }
                        break;
                }

                if (triggered) {
                    results.push({
                        triggered: true,
                        memoryCard: card,
                        reason,
                        prompt: this.generateTriggerPrompt(card, reason),
                    });
                }
            }

            return results;
        } catch (error) {
            this.logger.error(`检查触发器失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 生成触发器提示内容
     */
    private generateTriggerPrompt(card: MemoryCard, reason: string): string {
        return `[记忆触发] ${reason}\n相关记忆: ${card.content}\n建议: 根据这个记忆信息调整回应，展现出对用户的关心和了解。`;
    }

    /**
     * 创建后续追踪任务
     */
    async createFollowUpTask(
        task: Omit<FollowUpTask, "id" | "createdAt">
    ): Promise<MemoryCardOperationResult<FollowUpTask>> {
        try {
            const now = new Date().toISOString();
            const newTask: FollowUpTask = {
                ...task,
                id: generateFollowUpTaskId(),
                createdAt: now,
            };

            await this.ctx.database.create(TableName.FollowUpTasks, newTask);

            return {
                success: true,
                data: newTask,
            };
        } catch (error) {
            this.logger.error(`创建后续追踪任务失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 获取后续追踪任务
     */
    async getFollowUpTasks(
        userId?: string,
        status?: FollowUpTask["status"]
    ): Promise<MemoryCardOperationResult<FollowUpTask[]>> {
        try {
            const query: any = {};

            if (userId) {
                query.userId = userId;
            }

            if (status) {
                query.status = status;
            }

            const tasks = await this.ctx.database.get(TableName.FollowUpTasks, query);

            return {
                success: true,
                data: tasks,
            };
        } catch (error) {
            this.logger.error(`获取后续追踪任务失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 完成后续追踪任务
     */
    async completeFollowUpTask(taskId: string): Promise<MemoryCardOperationResult<void>> {
        try {
            await this.ctx.database.set(
                TableName.FollowUpTasks,
                { id: taskId },
                {
                    status: "completed",
                }
            );

            return {
                success: true,
            };
        } catch (error) {
            this.logger.error(`完成后续追踪任务失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }

    /**
     * 生成记忆简报
     */
    async generateMemoryBrief(
        userId: string,
        messageContent?: string
    ): Promise<MemoryCardOperationResult<MemoryBrief>> {
        try {
            const now = new Date().toISOString();
            const brief: MemoryBrief = {
                userId,
                triggeredCards: [],
                relevantCards: [],
                pendingFollowUps: [],
                prompts: [],
                generatedAt: now,
            };

            // 检查触发器
            if (messageContent) {
                const triggerResults = await this.checkTriggers(userId, messageContent);
                brief.triggeredCards = triggerResults
                    .filter((r) => r.triggered && r.memoryCard)
                    .map((r) => r.memoryCard!);
                brief.prompts = triggerResults.filter((r) => r.prompt).map((r) => r.prompt!);
            }

            // 获取相关记忆卡片
            const searchResult = await this.searchMemoryCards({
                userId,
                states: ["Active"],
                limit: 10,
                sortBy: "importance",
                sortOrder: "desc",
            });

            if (searchResult.success && searchResult.data) {
                brief.relevantCards = searchResult.data;
            }

            // 获取待处理的后续追踪任务
            const tasksResult = await this.getFollowUpTasks(userId, "pending");
            if (tasksResult.success && tasksResult.data) {
                const now = new Date();
                brief.pendingFollowUps = tasksResult.data.filter((task) => {
                    const scheduledTime = new Date(task.scheduledAt);
                    return now >= scheduledTime;
                });
            }

            return {
                success: true,
                data: brief,
            };
        } catch (error) {
            this.logger.error(`生成记忆简报失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                code: "DATABASE_ERROR",
            };
        }
    }
}
