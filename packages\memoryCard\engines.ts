import { Context, Logger } from "koishi";

import { Services } from "@/services/types";
import { CaptureEngineConfig, ContextTriggerEngineConfig, FollowUpEngineConfig, RefineryEngineConfig } from "./config";
import { FollowUpTask, MemoryBrief, MemoryCard, TriggerCheckResult, UserMemory } from "./interfaces";
import {
    calculateFollowUpScheduleTime,
    generateCardId,
    generateFollowUpTaskId,
    isContextTriggerActive,
    isTimeAndContextTriggerActive,
    isTimeTriggerActive,
} from "./utils";
import { FoldedDialogueSegment } from "../worldstate";

/**
 * 记忆提炼引擎
 * 负责从对话片段中提炼出结构化的记忆卡片
 */
export class RefineryEngine {
    private logger: Logger;

    constructor(private ctx: Context, private config: RefineryEngineConfig) {
        this.logger = ctx[Services.Logger].getLogger("[记忆提炼引擎]");
    }

    /**
     * 从对话片段提炼记忆卡片
     */
    // async refineSegment(segment: FoldedDialogueSegment): Promise<MemoryCard[]> {
    //     try {
    //         if (!this.config.autoRefineEnabled) {
    //             this.logger.debug("自动提炼已禁用，跳过片段处理");
    //             return [];
    //         }

    //         if (segment.messages.length < this.config.refineThreshold) {
    //             this.logger.debug(`片段 ${segment.id} 长度不足提炼阈值，跳过`);
    //             return [];
    //         }

    //         this.logger.debug(`开始提炼片段 ${segment.id}`);

    //         // 分析对话内容
    //         const analysisResult = await this.analyzeDialogue(segment);

    //         // 生成记忆卡片
    //         const cards = await this.generateMemoryCards(segment, analysisResult);

    //         this.logger.debug(`从片段 ${segment.id} 提炼出 ${cards.length} 张记忆卡片`);
    //         return cards;
    //     } catch (error) {
    //         this.logger.error(`提炼片段失败: ${error.message}`);
    //         return [];
    //     }
    // }

    /**
     * 分析对话内容
     */
    // private async analyzeDialogue(segment: FoldedDialogueSegment): Promise<any> {
    //     // TODO: 使用LLM分析对话内容，提取关键信息
    //     // 这里先返回一个简单的分析结果
    //     const userMessages = segment.messages.filter((m) => m.isFromUser);
    //     const keywords = this.extractKeywords(userMessages.map((m) => m.content).join(" "));

    //     return {
    //         keywords,
    //         topics: this.identifyTopics(userMessages),
    //         facts: this.extractFacts(userMessages),
    //         preferences: this.extractPreferences(userMessages),
    //         events: this.extractEvents(userMessages),
    //     };
    // }

    /**
     * 生成记忆卡片
     */
    private async generateMemoryCards(segment: FoldedDialogueSegment, analysis: any): Promise<MemoryCard[]> {
        const cards: MemoryCard[] = [];
        const now = new Date().toISOString();

        // 生成事实卡片
        for (const fact of analysis.facts) {
            cards.push({
                card_id: generateCardId(),
                type: "Fact",
                content: fact.content,
                keywords: fact.keywords,
                source_message_ids: fact.messageIds,
                created_at: now,
                last_accessed: now,
                importance: this.calculateImportance(fact),
                state: "Active",
            });
        }

        // 生成偏好卡片
        for (const preference of analysis.preferences) {
            cards.push({
                card_id: generateCardId(),
                type: "Preference",
                content: preference.content,
                keywords: preference.keywords,
                source_message_ids: preference.messageIds,
                created_at: now,
                last_accessed: now,
                importance: this.calculateImportance(preference),
                state: "Active",
            });
        }

        // 生成事件卡片
        for (const event of analysis.events) {
            cards.push({
                card_id: generateCardId(),
                type: "Event",
                content: event.content,
                keywords: event.keywords,
                source_message_ids: event.messageIds,
                created_at: now,
                last_accessed: now,
                importance: this.calculateImportance(event),
                state: "Active",
                trigger: event.trigger,
                followUp: event.followUp,
            });
        }

        return cards;
    }

    /**
     * 提取关键词
     */
    private extractKeywords(text: string): string[] {
        // 简单的关键词提取逻辑
        const words = text
            .toLowerCase()
            .replace(/[^\w\s\u4e00-\u9fff]/g, " ")
            .split(/\s+/)
            .filter((word) => word.length > 1);

        // 去重并返回
        return [...new Set(words)].slice(0, 10);
    }

    /**
     * 识别话题
     */
    private identifyTopics(messages: any[]): string[] {
        // TODO: 实现话题识别逻辑
        return [];
    }

    /**
     * 提取事实
     */
    private extractFacts(messages: any[]): any[] {
        // TODO: 实现事实提取逻辑
        return [];
    }

    /**
     * 提取偏好
     */
    private extractPreferences(messages: any[]): any[] {
        // TODO: 实现偏好提取逻辑
        return [];
    }

    /**
     * 提取事件
     */
    private extractEvents(messages: any[]): any[] {
        // TODO: 实现事件提取逻辑
        return [];
    }

    /**
     * 计算重要性分数
     */
    private calculateImportance(item: any): number {
        // TODO: 实现重要性计算逻辑
        return Math.min(0.5, this.config.maxImportanceScore);
    }
}

/**
 * 情境触发器引擎
 * 负责在每次对话交互前检查并激活符合条件的触发器
 */
export class ContextTriggerEngine {
    private logger: Logger;

    constructor(private ctx: Context, private config: ContextTriggerEngineConfig) {
        this.logger = ctx[Services.Logger].getLogger("[情境触发器引擎]");
    }

    /**
     * 检查用户记忆中的触发器
     */
    async checkTriggers(
        userMemory: UserMemory,
        messageContent: string,
        currentTime: Date = new Date()
    ): Promise<TriggerCheckResult[]> {
        try {
            if (!this.config.enabled) {
                return [];
            }

            const results: TriggerCheckResult[] = [];

            for (const card of userMemory.memory_cards) {
                if (!card.trigger || card.state === "Archived") {
                    continue;
                }

                const triggerResult = this.checkSingleTrigger(card, messageContent, currentTime);
                if (triggerResult.triggered) {
                    results.push(triggerResult);
                }

                // 限制结果数量
                if (results.length >= this.config.maxTriggerResults) {
                    break;
                }
            }

            if (results.length > 0) {
                this.logger.debug(`检查到 ${results.length} 个触发器被激活`);
            }

            return results;
        } catch (error) {
            this.logger.error(`检查触发器失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 检查单个触发器
     */
    private checkSingleTrigger(card: MemoryCard, messageContent: string, currentTime: Date): TriggerCheckResult {
        let triggered = false;
        let reason = "";

        switch (card.trigger!.type) {
            case "Time":
                triggered = isTimeTriggerActive(card.trigger, currentTime);
                if (triggered) {
                    reason = `时间触发器激活: ${card.trigger.description || "时间条件满足"}`;
                }
                break;

            case "Context":
                triggered = isContextTriggerActive(card.trigger, messageContent);
                if (triggered) {
                    reason = `上下文触发器激活: 检测到关键词 [${card.trigger.contextKeywords.join(", ")}]`;
                }
                break;

            case "TimeAndContext":
                triggered = isTimeAndContextTriggerActive(card.trigger, messageContent, currentTime);
                if (triggered) {
                    reason = `时间和上下文复合触发器激活: 时间和关键词条件都满足`;
                }
                break;

            case "EventCompletion":
                // 这种类型的触发器需要额外的上下文信息，在这里暂时跳过
                // 应该在更高层的服务中处理
                break;
        }

        return {
            triggered,
            memoryCard: triggered ? card : undefined,
            reason: triggered ? reason : undefined,
            prompt: triggered ? this.generateTriggerPrompt(card, reason) : undefined,
        };
    }

    /**
     * 生成触发器提示内容
     */
    private generateTriggerPrompt(card: MemoryCard, reason: string): string {
        const basePrompt = `[记忆触发] ${reason}\n相关记忆: ${card.content}`;

        switch (card.type) {
            case "Event":
                return `${basePrompt}\n建议: 根据这个事件记忆，主动关心用户的进展或状态。`;
            case "Preference":
                return `${basePrompt}\n建议: 根据用户的偏好调整回应，展现出对用户喜好的了解。`;
            case "Fact":
                return `${basePrompt}\n建议: 利用这个事实信息，让对话更加个性化和相关。`;
            case "Goal":
                return `${basePrompt}\n建议: 关注用户的目标进展，提供相关的支持或建议。`;
            case "Relationship":
                return `${basePrompt}\n建议: 考虑用户的人际关系背景，调整对话的语调和内容。`;
            default:
                return `${basePrompt}\n建议: 根据这个记忆信息调整回应，展现出对用户的关心和了解。`;
        }
    }

    /**
     * 批量检查多个用户的触发器
     */
    async batchCheckTriggers(
        userMemories: UserMemory[],
        messageContent: string,
        currentTime: Date = new Date()
    ): Promise<Map<string, TriggerCheckResult[]>> {
        const results = new Map<string, TriggerCheckResult[]>();

        for (const userMemory of userMemories) {
            const userResults = await this.checkTriggers(userMemory, messageContent, currentTime);
            if (userResults.length > 0) {
                results.set(userMemory.user_id, userResults);
            }
        }

        return results;
    }
}

/**
 * 长期追踪引擎
 * 负责处理延迟追踪和状态变更追踪任务
 */
export class FollowUpEngine {
    private logger: Logger;
    private checkTimer?: NodeJS.Timeout;

    constructor(private ctx: Context, private config: FollowUpEngineConfig) {
        this.logger = ctx[Services.Logger].getLogger("[长期追踪引擎]");
    }

    /**
     * 启动引擎
     */
    start() {
        if (!this.config.enabled) {
            this.logger.info("长期追踪引擎已禁用");
            return;
        }

        this.startPeriodicCheck();
        this.logger.info("长期追踪引擎已启动");
    }

    /**
     * 停止引擎
     */
    stop() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = undefined;
        }
        this.logger.info("长期追踪引擎已停止");
    }

    /**
     * 启动定期检查
     */
    private startPeriodicCheck() {
        this.checkTimer = setInterval(() => this.performCheck(), this.config.checkIntervalMinutes * 60 * 1000);
    }

    /**
     * 执行检查
     */
    private async performCheck() {
        try {
            this.logger.debug("开始执行后续追踪任务检查");

            // 这里需要访问数据库，但由于引擎是独立的，
            // 实际的数据库操作应该通过回调或服务接口来处理
            // 暂时记录日志
            this.logger.debug("后续追踪任务检查完成");
        } catch (error) {
            this.logger.error(`后续追踪任务检查失败: ${error.message}`);
        }
    }

    /**
     * 处理记忆卡片状态变更
     */
    async handleCardStateChange(
        card: MemoryCard,
        oldState: MemoryCard["state"],
        newState: MemoryCard["state"]
    ): Promise<FollowUpTask[]> {
        try {
            const tasks: FollowUpTask[] = [];

            if (!card.followUp) {
                return tasks;
            }

            const now = new Date().toISOString();

            switch (card.followUp.type) {
                case "TimeDelayed":
                    // 当卡片变为Archived状态时，创建延迟任务
                    if (newState === "Archived" && oldState !== "Archived") {
                        const scheduledAt = calculateFollowUpScheduleTime(card.followUp, new Date());

                        tasks.push({
                            id: generateFollowUpTaskId(),
                            cardId: card.card_id,
                            userId: "", // 需要从外部传入
                            type: card.followUp.type,
                            prompt: card.followUp.prompt,
                            scheduledAt: scheduledAt.toISOString(),
                            createdAt: now,
                            status: "pending",
                        });

                        this.logger.debug(`创建延迟追踪任务: ${card.card_id} -> ${scheduledAt.toISOString()}`);
                    }
                    break;

                case "OnStateChange":
                    // 当卡片状态变更为指定状态时，立即创建任务
                    if (newState === card.followUp.triggerState) {
                        tasks.push({
                            id: generateFollowUpTaskId(),
                            cardId: card.card_id,
                            userId: "", // 需要从外部传入
                            type: card.followUp.type,
                            prompt: card.followUp.prompt,
                            scheduledAt: now, // 立即执行
                            createdAt: now,
                            status: "pending",
                        });

                        this.logger.debug(`创建状态变更追踪任务: ${card.card_id} -> ${newState}`);
                    }
                    break;
            }

            return tasks;
        } catch (error) {
            this.logger.error(`处理卡片状态变更失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 检查待执行的追踪任务
     */
    async checkPendingTasks(tasks: FollowUpTask[]): Promise<FollowUpTask[]> {
        try {
            const now = new Date();
            const readyTasks: FollowUpTask[] = [];

            for (const task of tasks) {
                if (task.status !== "pending") {
                    continue;
                }

                const scheduledTime = new Date(task.scheduledAt);

                // 检查任务是否已到执行时间
                if (now >= scheduledTime) {
                    readyTasks.push(task);
                }

                // 检查任务是否已过期
                const createdTime = new Date(task.createdAt);
                const expirationTime = new Date(createdTime);
                expirationTime.setDate(expirationTime.getDate() + this.config.taskExpirationDays);

                if (now > expirationTime) {
                    // 标记任务为已取消
                    task.status = "cancelled";
                    this.logger.debug(`任务已过期: ${task.id}`);
                }

                // 限制处理数量
                if (readyTasks.length >= this.config.maxTasksPerCheck) {
                    break;
                }
            }

            if (readyTasks.length > 0) {
                this.logger.debug(`发现 ${readyTasks.length} 个待执行的追踪任务`);
            }

            return readyTasks;
        } catch (error) {
            this.logger.error(`检查待执行任务失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 生成追踪任务的提示内容
     */
    generateTaskPrompt(task: FollowUpTask, card?: MemoryCard): string {
        let basePrompt = `[后续追踪] ${task.prompt}`;

        if (card) {
            basePrompt += `\n相关记忆: ${card.content}`;

            switch (task.type) {
                case "TimeDelayed":
                    basePrompt += `\n建议: 这是一个延迟追踪任务，主动询问用户相关事件的后续情况。`;
                    break;
                case "OnStateChange":
                    basePrompt += `\n建议: 这是一个状态变更触发的追踪任务，及时关注用户的当前状态。`;
                    break;
            }
        }

        return basePrompt;
    }

    /**
     * 批量处理多个卡片的状态变更
     */
    async batchHandleStateChanges(
        changes: Array<{
            card: MemoryCard;
            oldState: MemoryCard["state"];
            newState: MemoryCard["state"];
            userId: string;
        }>
    ): Promise<FollowUpTask[]> {
        const allTasks: FollowUpTask[] = [];

        for (const change of changes) {
            const tasks = await this.handleCardStateChange(change.card, change.oldState, change.newState);

            // 设置用户ID
            tasks.forEach((task) => {
                task.userId = change.userId;
            });

            allTasks.push(...tasks);
        }

        return allTasks;
    }
}

/**
 * 记忆检索引擎配置
 */
export interface RetrievalEngineConfig {
    /** 是否启用语义搜索 */
    semanticSearchEnabled: boolean;
    /** 默认检索数量 */
    defaultLimit: number;
    /** 相关性阈值 */
    relevanceThreshold: number;
    /** 重要性权重 */
    importanceWeight: number;
    /** 时间衰减因子 */
    timeDecayFactor: number;
}

/**
 * 记忆检索引擎
 * 负责根据上下文检索最相关的记忆卡片
 */
export class RetrievalEngine {
    private logger: Logger;

    constructor(private ctx: Context, private config: RetrievalEngineConfig) {
        this.logger = ctx[Services.Logger].getLogger("[记忆检索引擎]");
    }

    /**
     * 检索相关记忆卡片
     */
    async retrieveRelevantCards(
        userMemory: UserMemory,
        query: string,
        options?: {
            limit?: number;
            types?: MemoryCard["type"][];
            states?: MemoryCard["state"][];
            minImportance?: number;
        }
    ): Promise<MemoryCard[]> {
        try {
            let cards = userMemory.memory_cards;

            // 应用基础过滤
            if (options?.types && options.types.length > 0) {
                cards = cards.filter((card) => options.types!.includes(card.type));
            }

            if (options?.states && options.states.length > 0) {
                cards = cards.filter((card) => options.states!.includes(card.state));
            }

            if (options?.minImportance !== undefined) {
                cards = cards.filter((card) => card.importance >= options.minImportance!);
            }

            // 计算相关性分数
            const scoredCards = cards.map((card) => ({
                card,
                score: this.calculateRelevanceScore(card, query),
            }));

            // 过滤低相关性卡片
            const relevantCards = scoredCards.filter((item) => item.score >= this.config.relevanceThreshold);

            // 排序并限制数量
            relevantCards.sort((a, b) => b.score - a.score);

            const limit = options?.limit || this.config.defaultLimit;
            const result = relevantCards.slice(0, limit).map((item) => item.card);

            this.logger.debug(`检索到 ${result.length} 张相关记忆卡片`);
            return result;
        } catch (error) {
            this.logger.error(`检索相关记忆卡片失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 计算相关性分数
     */
    private calculateRelevanceScore(card: MemoryCard, query: string): number {
        let score = 0;

        // 关键词匹配分数
        const keywordScore = this.calculateKeywordScore(card, query);

        // 内容匹配分数
        const contentScore = this.calculateContentScore(card, query);

        // 重要性分数
        const importanceScore = card.importance * this.config.importanceWeight;

        // 时间衰减分数
        const timeScore = this.calculateTimeScore(card);

        // 综合分数
        score = (keywordScore + contentScore) * 0.4 + importanceScore * 0.4 + timeScore * 0.2;

        return Math.min(1.0, Math.max(0.0, score));
    }

    /**
     * 计算关键词匹配分数
     */
    private calculateKeywordScore(card: MemoryCard, query: string): number {
        const queryWords = query.toLowerCase().split(/\s+/);
        const cardKeywords = card.keywords.map((k) => k.toLowerCase());

        let matches = 0;
        for (const word of queryWords) {
            if (cardKeywords.some((keyword) => keyword.includes(word) || word.includes(keyword))) {
                matches++;
            }
        }

        return queryWords.length > 0 ? matches / queryWords.length : 0;
    }

    /**
     * 计算内容匹配分数
     */
    private calculateContentScore(card: MemoryCard, query: string): number {
        const queryLower = query.toLowerCase();
        const contentLower = card.content.toLowerCase();

        // 简单的包含匹配
        if (contentLower.includes(queryLower)) {
            return 1.0;
        }

        // 部分词匹配
        const queryWords = queryLower.split(/\s+/);
        let matches = 0;

        for (const word of queryWords) {
            if (word.length > 2 && contentLower.includes(word)) {
                matches++;
            }
        }

        return queryWords.length > 0 ? matches / queryWords.length : 0;
    }

    /**
     * 计算时间分数（越新的记忆分数越高）
     */
    private calculateTimeScore(card: MemoryCard): number {
        const now = new Date();
        const cardTime = new Date(card.last_accessed);
        const daysDiff = (now.getTime() - cardTime.getTime()) / (1000 * 60 * 60 * 24);

        // 使用指数衰减
        return Math.exp(-daysDiff * this.config.timeDecayFactor);
    }

    /**
     * 按类型检索记忆卡片
     */
    async retrieveByType(userMemory: UserMemory, type: MemoryCard["type"], limit: number = 10): Promise<MemoryCard[]> {
        const cards = userMemory.memory_cards
            .filter((card) => card.type === type && card.state === "Active")
            .sort((a, b) => b.importance - a.importance)
            .slice(0, limit);

        return cards;
    }

    /**
     * 检索最近的记忆卡片
     */
    async retrieveRecent(userMemory: UserMemory, limit: number = 10, days: number = 7): Promise<MemoryCard[]> {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        const cards = userMemory.memory_cards
            .filter((card) => {
                const cardDate = new Date(card.last_accessed);
                return cardDate >= cutoffDate && card.state === "Active";
            })
            .sort((a, b) => new Date(b.last_accessed).getTime() - new Date(a.last_accessed).getTime())
            .slice(0, limit);

        return cards;
    }

    /**
     * 检索重要的记忆卡片
     */
    async retrieveImportant(
        userMemory: UserMemory,
        limit: number = 10,
        minImportance: number = 0.7
    ): Promise<MemoryCard[]> {
        const cards = userMemory.memory_cards
            .filter((card) => card.importance >= minImportance && card.state === "Active")
            .sort((a, b) => b.importance - a.importance)
            .slice(0, limit);

        return cards;
    }
}

/**
 * 记忆简报生成器配置
 */
export interface MemoryBriefGeneratorConfig {
    /** 最大触发卡片数量 */
    maxTriggeredCards: number;
    /** 最大相关卡片数量 */
    maxRelevantCards: number;
    /** 最大待处理任务数量 */
    maxPendingTasks: number;
    /** 是否包含用户画像 */
    includeUserProfile: boolean;
}

/**
 * 记忆简报生成器
 * 负责整合各种记忆信息生成简洁的记忆简报
 */
export class MemoryBriefGenerator {
    private logger: Logger;

    constructor(private ctx: Context, private config: MemoryBriefGeneratorConfig) {
        this.logger = ctx[Services.Logger].getLogger("[记忆简报生成器]");
    }

    /**
     * 生成完整的记忆简报
     */
    async generateBrief(
        userMemory: UserMemory,
        triggeredCards: MemoryCard[],
        relevantCards: MemoryCard[],
        pendingTasks: FollowUpTask[],
        messageContent?: string
    ): Promise<MemoryBrief> {
        try {
            const now = new Date().toISOString();

            // 限制数量
            const limitedTriggered = triggeredCards.slice(0, this.config.maxTriggeredCards);
            const limitedRelevant = relevantCards.slice(0, this.config.maxRelevantCards);
            const limitedTasks = pendingTasks.slice(0, this.config.maxPendingTasks);

            // 生成提示内容
            const prompts = this.generatePrompts(
                userMemory,
                limitedTriggered,
                limitedRelevant,
                limitedTasks,
                messageContent
            );

            const brief: MemoryBrief = {
                userId: userMemory.user_id,
                triggeredCards: limitedTriggered,
                relevantCards: limitedRelevant,
                pendingFollowUps: limitedTasks,
                prompts,
                generatedAt: now,
            };

            this.logger.debug(
                `为用户 ${userMemory.user_id} 生成记忆简报: ${limitedTriggered.length} 触发, ${limitedRelevant.length} 相关, ${limitedTasks.length} 任务`
            );
            return brief;
        } catch (error) {
            this.logger.error(`生成记忆简报失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 生成提示内容
     */
    private generatePrompts(
        userMemory: UserMemory,
        triggeredCards: MemoryCard[],
        relevantCards: MemoryCard[],
        pendingTasks: FollowUpTask[],
        messageContent?: string
    ): string[] {
        const prompts: string[] = [];

        // 用户画像提示
        if (this.config.includeUserProfile && userMemory.profile) {
            prompts.push(this.generateProfilePrompt(userMemory.profile));
        }

        // 触发器提示
        for (const card of triggeredCards) {
            prompts.push(this.generateTriggerPrompt(card));
        }

        // 相关记忆提示
        if (relevantCards.length > 0) {
            prompts.push(this.generateRelevantMemoryPrompt(relevantCards, messageContent));
        }

        // 待处理任务提示
        for (const task of pendingTasks) {
            prompts.push(this.generateTaskPrompt(task));
        }

        return prompts;
    }

    /**
     * 生成用户画像提示
     */
    private generateProfilePrompt(profile: UserMemory["profile"]): string {
        let prompt = `[用户画像] 用户: ${profile.name}`;

        if (profile.personas && profile.personas.length > 0) {
            prompt += `\n身份标签: ${profile.personas.join(", ")}`;
        }

        if (profile.summary) {
            prompt += `\n用户概况: ${profile.summary}`;
        }

        prompt += `\n建议: 根据用户的身份和特点调整对话风格和内容。`;

        return prompt;
    }

    /**
     * 生成触发器提示
     */
    private generateTriggerPrompt(card: MemoryCard): string {
        let prompt = `[记忆触发] 类型: ${card.type}\n内容: ${card.content}`;

        switch (card.type) {
            case "Event":
                prompt += `\n建议: 主动关心这个事件的进展或结果。`;
                break;
            case "Preference":
                prompt += `\n建议: 考虑用户的这个偏好来调整回应。`;
                break;
            case "Goal":
                prompt += `\n建议: 询问目标的进展或提供相关支持。`;
                break;
            case "Fact":
                prompt += `\n建议: 利用这个事实让对话更个性化。`;
                break;
            case "Relationship":
                prompt += `\n建议: 考虑用户的人际关系背景。`;
                break;
        }

        return prompt;
    }

    /**
     * 生成相关记忆提示
     */
    private generateRelevantMemoryPrompt(cards: MemoryCard[], messageContent?: string): string {
        let prompt = `[相关记忆] 找到 ${cards.length} 条相关记忆:`;

        for (const card of cards.slice(0, 3)) {
            // 只显示前3条
            prompt += `\n- ${card.type}: ${card.content.substring(0, 50)}${card.content.length > 50 ? "..." : ""}`;
        }

        if (messageContent) {
            prompt += `\n建议: 结合这些记忆信息来回应用户的当前消息。`;
        } else {
            prompt += `\n建议: 这些记忆可能与当前对话相关，适时引用。`;
        }

        return prompt;
    }

    /**
     * 生成任务提示
     */
    private generateTaskPrompt(task: FollowUpTask): string {
        return `[待处理任务] ${task.prompt}\n建议: 在合适的时机主动执行这个追踪任务。`;
    }

    /**
     * 生成简化版简报（用于快速检查）
     */
    async generateQuickBrief(
        userMemory: UserMemory,
        messageContent?: string
    ): Promise<{
        hasTriggeredMemories: boolean;
        hasRelevantMemories: boolean;
        hasPendingTasks: boolean;
        summary: string;
    }> {
        try {
            // 这里需要实际的触发器检查和检索逻辑
            // 暂时返回一个简化的结果
            const activeCards = userMemory.memory_cards.filter((card) => card.state === "Active");

            return {
                hasTriggeredMemories: false, // 需要实际的触发器检查
                hasRelevantMemories: activeCards.length > 0,
                hasPendingTasks: false, // 需要实际的任务检查
                summary: `用户 ${userMemory.user_id} 有 ${activeCards.length} 条活跃记忆`,
            };
        } catch (error) {
            this.logger.error(`生成快速简报失败: ${error.message}`);
            return {
                hasTriggeredMemories: false,
                hasRelevantMemories: false,
                hasPendingTasks: false,
                summary: "简报生成失败",
            };
        }
    }
}
