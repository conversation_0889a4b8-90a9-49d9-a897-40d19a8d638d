import { generateText } from "xsai";

// --- 配置区 ---

// 1. 设置要重写的 Git commit 范围。
//    'HEAD~27' 表示重写最近的27个提交。
//    'main..HEAD' 表示重写当前分支相对于main分支多出来的所有提交。
const COMMIT_RANGE = "20";

// 2. XSAI LLM 后端配置
//    重要提示：这里假设 xsai 提供了与 OpenAI 兼容的 v1/chat/completions 接口。
//    如果 URL 不同，请修改此处。
const XSAI_API_URL = "https://api.gptgod.online/v1";
const XSAI_API_KEY = process.env.API_KEY_GOD;
const LLM_MODEL = "glm-4-flash";

// --- 脚本主逻辑 ---

/**
 * 使用LLM API生成新的commit message。
 * @param {string} oldMessage - 旧的提交信息
 * @param {string} diff - 代码变更内容
 * @returns {Promise<string>} - 新的提交信息
 */
async function generateNewCommitMessage(oldMessage, diff) {
    if (!XSAI_API_KEY) {
        console.error("错误：未设置 XSAI_API_KEY 环境变量。");
        process.exit(1);
    }

    // 精心设计的Prompt，引导LLM输出高质量结果
    const prompt = `
你是一名专业的Git和软件开发专家，精通Conventional Commits规范。
你的任务是根据一个旧的、不规范的Git commit信息和对应的代码变更(diff)，生成一个全新的、符合规范的、包含标题和描述正文的完整commit message。
使用中文输出。

**规范格式:**
<type>(<scope>): <subject>

[可选的，简短的描述正文]

**常见类型:**
- \`feat\`: 新功能 (feature)
- \`fix\`: 修复bug
- \`docs\`: 文档变更
- \`style\`: 代码格式（不影响代码运行的变动）
- \`refactor\`: 重构（既不是新增功能，也不是修改bug的代码变动）
- \`perf\`: 性能优化
- \`test\`: 增加测试
- \`chore\`: 构建过程或辅助工具的变动
- \`ci\`: CI配置文件和脚本的变动

**要求:**
1.  **严格遵守** Conventional Commits 格式。
2.  \`scope\` 应该是可选的，并且能准确描述受影响的代码模块（例如: \`api\`, \`ui\`, \`core\`, \`memory\`)。如果影响范围很广，可以省略 scope。
3.  \`subject\` (标题) 应该是简短、清晰、使用祈使句的描述。
4.  **标题行和正文之间必须有一个空行**，这是Git的规范。
5.  **正文部分 (body)** 应该用1-3句话简要解释变更的**背景和目的**。它应该说明“为什么”要进行此项更改，而不仅仅是“什么”被更改了。
6.  如果变更很简单，不需要额外解释，则可以省略正文。
7.  **只输出最终完整的 commit message**，不要包含任何额外的解释、引号或前缀。

---
**输入:**

**旧 Commit Message:**
\`\`\`
${oldMessage}
\`\`\`

**代码变更 (Diff):**
\`\`\`diff
${diff.substring(0, 4000)}
\`\`\`

---
**输出 (完整的 Commit Message，包括标题和可选的正文):**
`;

    try {
        const response = await generateText({
            apiKey: XSAI_API_KEY,
            baseURL: XSAI_API_URL,
            model: LLM_MODEL,
            messages: [{ role: "user", content: prompt }],
            temperature: 0.2,
        });

        const newMessage = response.text;

        if (!newMessage) {
            throw new Error("API返回的数据格式不正确，无法找到新消息。");
        }

        // 清理LLM可能返回的多余字符
        return newMessage.replace(/[`"']/g, "");
    } catch (error) {
        console.error(`调用LLM API时出错: ${error.message}`);
        // 出错时返回一个带标记的旧消息，方便手动处理
        return `fix: [LLM FAILED] ${oldMessage}`;
    }
}

/**
 * 获取指定范围内的commit SHA列表 (从旧到新)
 * @param {string} range - git log 范围
 * @returns {string[]} - Commit SHA 数组
 */
function getCommitsToRewrite(range) {
    const proc = Bun.spawnSync(["git", "log", "--reverse", "--pretty=%H", "-n", range]);
    if (proc.exitCode !== 0) {
        console.error(`获取commit列表失败: ${new TextDecoder().decode(proc.stderr)}`);
        return [];
    }
    const output = new TextDecoder().decode(proc.stdout).trim();
    return output ? output.split("\n") : [];
}

/**
 * 获取单个commit的旧信息和diff
 * @param {string} sha - Commit SHA
 * @returns {{oldMessage: string, diff: string} | null}
 */
function getCommitDetails(sha) {
    const messageProc = Bun.spawnSync(["git", "log", "-1", "--pretty=%s", sha]);
    const diffProc = Bun.spawnSync(["git", "show", sha]);

    if (messageProc.exitCode !== 0 || diffProc.exitCode !== 0) {
        console.error(`获取commit ${sha} 详情失败`);
        return null;
    }

    return {
        oldMessage: new TextDecoder().decode(messageProc.stdout).trim(),
        diff: new TextDecoder().decode(diffProc.stdout).trim(),
    };
}

/**
 * 主函数
 */
async function main() {
    console.log(`正在处理范围 '${COMMIT_RANGE}' 内的提交...`);
    const commits = getCommitsToRewrite(COMMIT_RANGE);

    if (commits.length === 0) {
        console.log("在指定范围内未找到任何提交。");
        return;
    }

    const rebasePlan = [];

    for (const sha of commits) {
        const details = getCommitDetails(sha);
        if (!details) continue;

        const shortSha = sha.substring(0, 7);
        console.log(`\n🔄 正在处理 ${shortSha}: ${details.oldMessage}`);

        const newMessage = await generateNewCommitMessage(details.oldMessage, details.diff);
        console.log(`✅ 新标题: ${newMessage}`);

        rebasePlan.push({ shortSha, newMessage });
    }

    console.log("\n\n--- 🚀 Git Rebase 操作指南 🚀 ---\n");
    console.log("1. **启动交互式 Rebase**");
    const baseCommit = `${COMMIT_RANGE}~1`; // e.g., HEAD~27~1 -> HEAD~28
    console.log(`   运行以下命令 (请根据你的范围确认基准commit):\n   git rebase -i ${baseCommit}\n`);

    console.log("2. **修改 Rebase 计划**");
    console.log("   在打开的编辑器中，将所有需要修改的行的 'pick' 改为 'reword' (或 'r')。");
    console.log("   保存并关闭编辑器。\n");

    console.log("3. **逐一应用新标题**");
    console.log("   Git会为每个 'reword' 的提交暂停，并打开一个新的编辑器让你修改提交信息。");
    console.log("   请从下面的列表中复制对应的新标题，粘贴并替换旧标题，然后保存关闭。");
    console.log("   重复此过程直到 Rebase 完成。\n");

    console.log("--- 📝 新的 Commit 标题列表 (供复制使用) ---\n");
    for (const plan of rebasePlan) {
        console.log(`${plan.shortSha} -> ${plan.newMessage}`);
    }

    console.log("\n--- ⚠️ 重要提醒 ---");
    console.log("修改历史后，如果分支已推送到远程，你需要强制推送。");
    console.log("推荐使用更安全的 `git push --force-with-lease` 命令。");
}

// 当脚本直接被bun执行时，运行main函数
if (import.meta.main) {
    main();
}
